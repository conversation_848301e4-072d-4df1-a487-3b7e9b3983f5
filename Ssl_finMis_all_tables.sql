-- Ssl_finMis: Complete Database Schema
-- This file contains all tables required for the Ssl_finMis finance management system.

-- 1. User Management (Yii2 style)
CREATE TABLE user (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL UNIQUE,
    auth_key VARCHAR(32) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    password_reset_token VARCHAR(255) DEFAULT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    status SMALLINT NOT NULL DEFAULT 10,
    created_at INT NOT NULL,
    updated_at INT NOT NULL
);

CREATE TABLE auth_assignment (
    item_name VARCHAR(64) NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    created_at INT,
    PRIMARY KEY (item_name, user_id),
    FOREIGN KEY (user_id) REFERENCES user(id)
);

CREATE TABLE auth_item (
    name <PERSON><PERSON>HA<PERSON>(64) NOT NULL PRIMARY KEY,
    type INT NOT NULL,
    description TEXT,
    rule_name VARCHAR(64),
    data BLOB,
    created_at INT,
    updated_at INT
);

CREATE TABLE auth_item_child (
    parent VARCHAR(64) NOT NULL,
    child VARCHAR(64) NOT NULL,
    PRIMARY KEY (parent, child),
    FOREIGN KEY (parent) REFERENCES auth_item(name),
    FOREIGN KEY (child) REFERENCES auth_item(name)
);

CREATE TABLE auth_rule (
    name VARCHAR(64) NOT NULL PRIMARY KEY,
    data BLOB,
    created_at INT,
    updated_at INT
);

-- 2. Core Finance Tables
CREATE TABLE accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    description TEXT,
    balance DECIMAL(18,2) DEFAULT 0.00,
    created_at DATETIME,
    updated_at DATETIME
);

CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type ENUM('income','expense','asset','liability','equity') NOT NULL,
    parent_id INT DEFAULT NULL,
    description TEXT,
    FOREIGN KEY (parent_id) REFERENCES categories(id)
);

CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_id INT NOT NULL,
    category_id INT NOT NULL,
    user_id INT UNSIGNED,
    amount DECIMAL(18,2) NOT NULL,
    type ENUM('income','expense','transfer') NOT NULL,
    description TEXT,
    transaction_date DATE NOT NULL,
    created_at DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (account_id) REFERENCES accounts(id),
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (user_id) REFERENCES user(id)
);

CREATE TABLE budgets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED,
    category_id INT NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    created_at DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

CREATE TABLE recurring_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED,
    account_id INT NOT NULL,
    category_id INT NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    type ENUM('income','expense','transfer') NOT NULL,
    description TEXT,
    frequency ENUM('daily','weekly','monthly','yearly') NOT NULL,
    next_run_date DATE NOT NULL,
    end_date DATE,
    created_at DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (account_id) REFERENCES accounts(id),
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- 3. Advanced Features
CREATE TABLE vendors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_info TEXT,
    created_at DATETIME,
    updated_at DATETIME
);

CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_info TEXT,
    created_at DATETIME,
    updated_at DATETIME
);

CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    total_amount DECIMAL(18,2) NOT NULL,
    status ENUM('draft','sent','paid','overdue','cancelled') NOT NULL,
    created_at DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

CREATE TABLE invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    description VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit_price DECIMAL(18,2) NOT NULL,
    total DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);

CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT,
    customer_id INT,
    amount DECIMAL(18,2) NOT NULL,
    payment_date DATE NOT NULL,
    method VARCHAR(50),
    reference VARCHAR(100),
    created_at DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

CREATE TABLE expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    vendor_id INT,
    category_id INT NOT NULL,
    account_id INT NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    expense_date DATE NOT NULL,
    description TEXT,
    created_at DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id),
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);

CREATE TABLE assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    value DECIMAL(18,2) NOT NULL,
    acquisition_date DATE,
    depreciation_rate DECIMAL(5,2),
    created_at DATETIME,
    updated_at DATETIME
);

CREATE TABLE liabilities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    value DECIMAL(18,2) NOT NULL,
    due_date DATE,
    created_at DATETIME,
    updated_at DATETIME
);

CREATE TABLE audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED,
    action VARCHAR(255) NOT NULL,
    details TEXT,
    created_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES user(id)
);

-- 4. Compliance & Permissions
CREATE TABLE permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT
);

CREATE TABLE user_permissions (
    user_id INT UNSIGNED NOT NULL,
    permission_id INT NOT NULL,
    granted_at DATETIME,
    PRIMARY KEY (user_id, permission_id),
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (permission_id) REFERENCES permissions(id)
);

-- 5. Reports & Analytics
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    parameters TEXT,
    created_by INT UNSIGNED,
    created_at DATETIME,
    FOREIGN KEY (created_by) REFERENCES user(id)
);

-- 6. Attachments (for receipts, invoices, etc.)
CREATE TABLE attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    related_type VARCHAR(50) NOT NULL,
    related_id INT NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    uploaded_by INT UNSIGNED,
    uploaded_at DATETIME,
    FOREIGN KEY (uploaded_by) REFERENCES user(id)
);

-- 7. Notifications
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES user(id)
);

-- 8. Settings
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    value TEXT,
    updated_at DATETIME
);

-- End of unified schema
