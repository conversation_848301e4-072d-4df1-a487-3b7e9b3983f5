#!/bin/bash
# Bulk image optimization script for hotel-bnb
# Requires: jpegoptim, optipng, webp (cwebp)

# Folders to optimize
FOLDERS=(bar bnb coffee conference deluxe dinning hsb kitchen rooms single southernblue standard)

# Optimize JPEGs
for folder in "${FOLDERS[@]}"; do
  if [ -d "$folder" ]; then
    find "$folder" -type f \( -iname "*.jpg" -o -iname "*.jpeg" \) -exec jpegoptim --strip-all --max=85 {} \;
  fi
done

# Optimize PNGs
for folder in "${FOLDERS[@]}"; do
  if [ -d "$folder" ]; then
    find "$folder" -type f -iname "*.png" -exec optipng -o7 {} \;
  fi
done

# (Optional) Convert to WebP
for folder in "${FOLDERS[@]}"; do
  if [ -d "$folder" ]; then
    find "$folder" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" \) -exec sh -c 'cwebp -q 80 "$0" -o "${0%.*}.webp"' {} \;
  fi
done

echo "Image optimization complete."
