<?php
// send-email.php
// Usage: POST to this file with 'to', 'subject', and 'message' fields

// SMTP config (edit as needed)
$smtp_host = 'smtp.gmail.com';
$smtp_port = 587;
$smtp_username = '<EMAIL>';
$smtp_password = 'Aug205/HSB';
$smtp_secure = 'tls'; // or 'ssl'
$from_email = '<EMAIL>';
$from_name = 'Hotel Southern Blue';



// Accept parameters from any method (GET, POST, REQUEST)
$to = isset($_REQUEST['to']) ? trim($_REQUEST['to']) : '';
$subject = isset($_REQUEST['subject']) ? trim($_REQUEST['subject']) : '';
$message = isset($_REQUEST['body']) ? trim($_REQUEST['body']) : (isset($_REQUEST['message']) ? trim($_REQUEST['message']) : '');

// Debug: Show all request data if ?debug=1 is set
if (isset($_REQUEST['debug'])) {
    header('Content-Type: text/plain');
    echo "_GET: "; var_export($_GET); echo "\n";
    echo "_POST: "; var_export($_POST); echo "\n";
    echo "_REQUEST: "; var_export($_REQUEST); echo "\n";
    echo "to: $to\nsubject: $subject\nmessage: $message\n";
    exit;
}

if (!$to) {
    http_response_code(400);
    echo 'Missing recipient email (to).';
    exit;
}
if (!$subject) {
    http_response_code(400);
    echo 'Missing subject.';
    exit;
}
if (!$message) {
    http_response_code(400);
    echo 'Missing message.';
    exit;
}

// Use PHPMailer
require_once __DIR__ . '/wp-includes/PHPMailer/PHPMailer.php';
require_once __DIR__ . '/wp-includes/PHPMailer/SMTP.php';
require_once __DIR__ . '/wp-includes/PHPMailer/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

$mail = new PHPMailer(true);

try {
    // Server settings
    $mail->isSMTP();
    $mail->Host = $smtp_host;
    $mail->SMTPAuth = true;
    $mail->Username = $smtp_username;
    $mail->Password = $smtp_password;
    $mail->SMTPSecure = $smtp_secure;
    $mail->Port = $smtp_port;

    // Recipients
    $mail->setFrom($from_email, $from_name);
    $mail->addAddress($to);

    // Content
    $mail->isHTML(false);
    $mail->Subject = $subject;
    $mail->Body    = $message;

    $mail->send();
    echo 'Email sent successfully.';
} catch (Exception $e) {
    http_response_code(500);
    echo 'Mailer Error: ' . $mail->ErrorInfo;
}
