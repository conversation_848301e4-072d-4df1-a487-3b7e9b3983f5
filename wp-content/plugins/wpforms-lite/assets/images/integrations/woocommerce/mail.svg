<svg xmlns="http://www.w3.org/2000/svg" width="175" height="104" fill="none" viewBox="0 0 175 104">
  <mask id="a" width="121" height="104" x="-1" y="0" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="M0 .017h119.856V104H0V.017Z"/>
  </mask>
  <g mask="url(#a)">
    <path fill="#86B3E0" fill-rule="evenodd" d="M73.626 14.8h-52.97c-11.363 0-20.66 7.834-20.66 17.407v32.877h73.626V14.8h.004Z" clip-rule="evenodd"/>
    <path fill="#A3D1FF" fill-rule="evenodd" d="M73.626 14.801c-11.362 0-20.659 7.833-20.659 17.406v32.877h41.314V32.207c0-9.573-9.297-17.406-20.659-17.406h.004Z" clip-rule="evenodd"/>
    <path fill="#598BBD" fill-rule="evenodd" d="M73.626 16.302c-10.4 0-18.912 7.17-18.912 15.934v32.848h37.824V32.236c0-8.763-8.512-15.934-18.912-15.934Z" clip-rule="evenodd"/>
    <path fill="#598BBD" d="M-.003 32.205h52.97v32.877H-.002V32.205Z" opacity=".5"/>
    <path fill="#598BBD" d="M54.714 63.399h65.142v1.686H54.714v-1.686Zm-20.079 1.685h8.125V104h-8.125V65.084Z"/>
    <path fill="#86B3E0" d="M40.729 65.084h2.031V104h-2.031V65.084Z"/>
    <path fill="#598BBD" d="M36.54.018h2.665v42.81H36.54V.017Z"/>
    <path fill="#A3D1FF" d="M38.48.018h.725v42.81h-.725V.017Z"/>
    <path fill="#A3D1FF" fill-rule="evenodd" d="M38.457 38.203c-3.03 0-5.485 2.068-5.485 4.62 0 2.554 2.455 4.622 5.485 4.622 3.03 0 5.484-2.068 5.484-4.621 0-2.553-2.454-4.621-5.484-4.621Z" clip-rule="evenodd"/>
    <path fill="#598BBD" fill-rule="evenodd" d="M37.21 38.203c-3.03 0-5.484 2.068-5.484 4.62 0 2.554 2.454 4.622 5.485 4.622 3.03 0 5.484-2.068 5.484-4.621 0-2.553-2.455-4.621-5.484-4.621Z" clip-rule="evenodd"/>
    <path fill="#A3D1FF" fill-rule="evenodd" d="M37.21 41.329c-.982 0-1.777.67-1.777 1.497 0 .828.795 1.498 1.777 1.498.982 0 1.777-.67 1.777-1.498 0-.827-.795-1.497-1.777-1.497Z" clip-rule="evenodd"/>
    <path fill="#FF5454" fill-rule="evenodd" d="M38.484.018H21.777l3.294 4.028-3.294 4.027h16.707V.018Z" clip-rule="evenodd"/>
  </g>
  <path fill="#F2A674" fill-rule="evenodd" d="m114.039 37.368 9.476 10.49c2.462-.692 3.176-3.47 1.649-5.248-2.041-2.379-2.752-3.488-4.797-5.87l6.195-4.501-6.551-8.252-5.975 13.38h.003Z" clip-rule="evenodd"/>
  <path fill="#F2A674" fill-rule="evenodd" d="m112.14 35.647 5.24 13.584c2.756.325 4.11-2.767 3.301-4.987-1.08-2.966-.877-4.444-1.957-7.41l3.593-10.805-7.032-2.165-3.145 11.783Z" clip-rule="evenodd"/>
  <path fill="#F2A674" fill-rule="evenodd" d="m107.624 35.217 3.897 14.712c2.6-.005 4.344-2.068 3.765-4.472-.779-3.22-.84-5.192-1.618-8.415l5.227-10.543-4.692-2.826-6.579 11.546v-.002Z" clip-rule="evenodd"/>
  <mask id="b" width="34" height="24" x="100" y="19" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="M133.019 22.714 122.1 19.385c-.863-.277-20.773 5.563-21.742 6.139-.887 1.255.071 7.707.931 9.923-.006 1.438.088 4.807.156 6.815 3.981.27 4.811-3.36 4.879-6.398.328-1.375.721-4.69-.2-6.216 3.311-.513 9.354-1.12 12.618-1.078l4.235 6.275 10.042-12.134v.003Z"/>
  </mask>
  <g mask="url(#b)">
    <path fill="url(#c)" d="m117.502 5.016 29.377 26.682-31.895 24.928-29.377-26.682 31.895-24.928Z"/>
  </g>
  <mask id="d" width="74" height="34" x="54" y="30" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="M54.714 30.641h73.094V63.4H54.714V30.64Z"/>
  </mask>
  <g mask="url(#d)">
    <mask id="e" width="70" height="32" x="42" y="32" maskUnits="userSpaceOnUse" style="mask-type:luminance">
      <path fill="#fff" d="M42.776 32.204h69.015V63.39H42.776V32.204Z"/>
    </mask>
    <g mask="url(#e)">
      <path fill="url(#f)" d="m51.333 13.037 72.658 23.034-20.76 46.485-72.658-23.034 20.76-46.485Z"/>
    </g>
    <path fill="#E7E7E7" fill-rule="evenodd" d="m75.352 43.64-30.49 16.096c-1.321.696-2.086 1.94-2.086 3.391v.263h69.014v-.263c0-1.449-.765-2.692-2.085-3.391L79.215 43.64a4.052 4.052 0 0 0-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="m75.352 44.05-30.49 15.763c-1.321.682-2.086 1.9-2.086 3.32v.257h69.014v-.257c0-1.42-.765-2.638-2.085-3.32L79.215 44.05a4.116 4.116 0 0 0-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="M75.352 44.461c-10.164 5.143-20.327 10.286-30.49 15.426-1.321.668-2.086 1.86-2.086 3.25v.25h69.014v-.25c0-1.39-.765-2.582-2.085-3.25l-30.49-15.426a4.198 4.198 0 0 0-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="M75.352 44.872c-10.164 5.032-20.327 10.06-30.49 15.093-1.321.653-2.086 1.82-2.086 3.18v.248h69.014v-.248c0-1.36-.765-2.525-2.085-3.18-10.164-5.032-20.327-10.061-30.49-15.093a4.285 4.285 0 0 0-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="m75.352 45.283-30.49 14.758c-1.321.64-2.086 1.78-2.086 3.11v.242h69.014v-.242c0-1.33-.765-2.47-2.085-3.11l-30.49-14.758a4.377 4.377 0 0 0-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="M75.352 45.693C65.188 50.5 55.025 55.31 44.862 60.115c-1.321.625-2.086 1.74-2.086 3.038v.237h69.014v-.237c0-1.298-.765-2.413-2.085-3.038-10.164-4.806-20.327-9.615-30.49-14.422a4.473 4.473 0 0 0-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="m75.352 46.104-30.49 14.088c-1.321.61-2.086 1.697-2.086 2.967v.23h69.014v-.23c0-1.27-.765-2.36-2.085-2.967l-30.49-14.088a4.552 4.552 0 0 0-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <mask id="g" width="70" height="19" x="42" y="45" maskUnits="userSpaceOnUse" style="mask-type:luminance">
      <path fill="#fff" d="m75.352 46.104-30.49 14.088c-1.321.61-2.086 1.697-2.086 2.967v.23h69.014v-.23c0-1.27-.765-2.36-2.085-2.967l-30.49-14.088a4.552 4.552 0 0 0-3.863 0Z"/>
    </mask>
    <g mask="url(#g)">
      <path fill="url(#h)" d="m108.319 32.805 8.114 29.682-70.177 13.617-8.114-29.68 70.177-13.619Z"/>
    </g>
    <path fill="#E7E7E7" fill-rule="evenodd" d="m75.352 50.497-30.49-14.91c-1.321-.644-2.086-1.797-2.086-3.14v-.243h69.014v.243c0 1.343-.765 2.496-2.085 3.14l-30.49 14.91a4.339 4.339 0 0 1-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="M75.352 50.33a122018.05 122018.05 0 0 1-30.49-14.773c-1.321-.639-2.086-1.783-2.086-3.112v-.242h69.014v.242c0 1.33-.765 2.473-2.085 3.112a122018.05 122018.05 0 0 0-30.49 14.773 4.358 4.358 0 0 1-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="M75.352 50.163a94405.188 94405.188 0 0 1-30.49-14.636c-1.321-.633-2.086-1.765-2.086-3.083v-.24h69.014v.24c0 1.318-.765 2.45-2.085 3.083-10.164 4.878-20.327 9.756-30.49 14.636a4.414 4.414 0 0 1-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="M75.352 49.995c-10.164-4.832-20.327-9.667-30.49-14.499-1.321-.628-2.086-1.748-2.086-3.055v-.237h69.014v.237c0 1.306-.765 2.427-2.085 3.055-10.164 4.832-20.327 9.667-30.49 14.499-1.24.59-2.624.59-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="M75.352 49.827c-10.164-4.787-20.327-9.576-30.49-14.362-1.321-.622-2.086-1.732-2.086-3.027v-.234h69.014v.234c0 1.295-.765 2.405-2.085 3.027-10.164 4.786-20.327 9.575-30.49 14.362a4.472 4.472 0 0 1-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="M75.352 49.658c-10.164-4.74-20.327-9.484-30.49-14.225-1.321-.616-2.086-1.714-2.086-2.995v-.234h69.014v.234c0 1.281-.765 2.382-2.085 2.995-10.164 4.741-20.327 9.485-30.49 14.225-1.24.58-2.624.58-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <path fill="#E7E7E7" fill-rule="evenodd" d="m75.352 49.493-30.49-14.088c-1.321-.61-2.086-1.7-2.086-2.967v-.231h69.014v.231c0 1.27-.765 2.359-2.085 2.967l-30.49 14.088a4.552 4.552 0 0 1-3.863 0Z" clip-rule="evenodd" opacity=".1" style="mix-blend-mode:multiply"/>
    <mask id="i" width="70" height="18" x="42" y="32" maskUnits="userSpaceOnUse" style="mask-type:luminance">
      <path fill="#fff" d="m75.353 49.485-30.49-14.089c-1.321-.61-2.086-1.7-2.086-2.966v-.231h69.015v.23c0 1.27-.766 2.36-2.086 2.967l-30.49 14.089a4.553 4.553 0 0 1-3.863 0Z"/>
    </mask>
    <g mask="url(#i)">
      <path fill="url(#j)" d="m110.894 25.57 3.32 24.251-70.524 6.854-3.32-24.25 70.524-6.854Z"/>
    </g>
  </g>
  <mask id="k" width="75" height="25" x="100" y="20" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="m174.485 37.524-24.217.026c-6.777 2.572-7.973 2.855-15.431 4.292-8.305-.07-12.232-.835-15.692.049-1.849.47-5.393 1.24-9.212 2.114-4.584 1.05-8.278.293-8.877-1.498-.599-1.794.125-3.548 6.182-4.963 8.146-1.9 9.967-1.327 13.065-2.567 3.098-1.241 5.038-4.068 5.038-4.068l5.779-8.654 13.176 3.163c5.675-.619 30.704-4.7 30.704-4.7l-.518 16.806h.003Z"/>
  </mask>
  <g mask="url(#k)">
    <path fill="url(#l)" d="m174.749 17.05 1.94 27.924-75.978 3.747-1.94-27.923 75.978-3.747Z"/>
  </g>
  <path fill="#F2A674" fill-rule="evenodd" d="M105.061 39.156c-.504-.018-1.801.308-2.346.541.667-.15 1.513-.387 2.112-.376 2.861.054 3.768 2.753 1.28 3.948-.454.217-1.402.41-2.173.55.579-.028 2.078-.322 2.542-.513 2.17-.899 2.252-4.02-1.415-4.15Z" clip-rule="evenodd"/>
  <defs>
    <linearGradient id="c" x1="103.083" x2="113.145" y1="40.906" y2="29.828" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F2A674"/>
      <stop offset="1" stop-color="#FFBA8C"/>
    </linearGradient>
    <linearGradient id="f" x1="74.304" x2="95.905" y1="54.486" y2="-13.65" gradientUnits="userSpaceOnUse">
      <stop stop-color="#E7E7E7"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="h" x1="72.853" x2="80.78" y1="38.592" y2="79.444" gradientUnits="userSpaceOnUse">
      <stop stop-color="#E7E7E7"/>
      <stop offset="1" stop-color="#FEFEFE"/>
    </linearGradient>
    <linearGradient id="j" x1="75.308" x2="77.827" y1="26.352" y2="52.267" gradientUnits="userSpaceOnUse">
      <stop stop-color="#E7E7E7"/>
      <stop offset="1" stop-color="#FEFEFE"/>
    </linearGradient>
    <linearGradient id="l" x1="136.947" x2="171.648" y1="33.221" y2="30.81" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFBA8C"/>
      <stop offset="1" stop-color="#FFBA8C"/>
    </linearGradient>
  </defs>
</svg>
