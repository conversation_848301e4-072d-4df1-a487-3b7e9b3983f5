//
// jQuery MiniColors: A tiny color picker built on jQuery
//
// Developed by <PERSON> for A Beautiful Site, LLC
//
// Licensed under the MIT license: http://opensource.org/licenses/MIT
//
!function(i){"function"==typeof define&&define.amd?define(["jquery"],i):"object"==typeof exports?module.exports=i(require("jquery")):i(jQuery)}(function(C){"use strict";function o(i){var t=i.parent();i.removeData("minicolors-initialized").removeData("minicolors-settings").removeProp("size").removeClass("minicolors-input"),t.before(i).remove()}function s(i){var t=i.parent(),o=t.find(".minicolors-panel"),s=i.data("minicolors-settings");!i.data("minicolors-initialized")||i.prop("disabled")||t.hasClass("minicolors-inline")||t.hasClass("minicolors-focus")||(a(),t.addClass("minicolors-focus"),o.animate?o.stop(!0,!0).fadeIn(s.showSpeed,function(){s.show&&s.show.call(i.get(0))}):(o.show(),s.show&&s.show.call(i.get(0))))}function a(){C(".minicolors-focus").each(function(){var i=C(this),t=i.find(".minicolors-input"),o=i.find(".minicolors-panel"),s=t.data("minicolors-settings");o.animate?o.fadeOut(s.hideSpeed,function(){s.hide&&s.hide.call(t.get(0)),i.removeClass("minicolors-focus")}):(o.hide(),s.hide&&s.hide.call(t.get(0)),i.removeClass("minicolors-focus"))})}function n(i,t,o){var s,a,n,r,e,c=i.parents(".minicolors").find(".minicolors-input"),l=c.data("minicolors-settings"),h=i.find("[class$=-picker]"),d=i.offset().left,p=i.offset().top,u=Math.round(t.pageX-d),g=Math.round(t.pageY-p),m=o?l.animationSpeed:0;t.originalEvent.changedTouches&&(u=t.originalEvent.changedTouches[0].pageX-d,g=t.originalEvent.changedTouches[0].pageY-p),u<0&&(u=0),g<0&&(g=0),u>i.width()&&(u=i.width()),g>i.height()&&(g=i.height()),i.parent().is(".minicolors-slider-wheel")&&h.parent().is(".minicolors-grid")&&(s=75-u,a=75-g,n=Math.sqrt(s*s+a*a),(r=Math.atan2(a,s))<0&&(r+=2*Math.PI),75<n&&(u=(n=75)-75*Math.cos(r),g=75-75*Math.sin(r)),u=Math.round(u),g=Math.round(g)),e={top:g+"px"},i.is(".minicolors-grid")&&(e.left=u+"px"),h.animate?h.stop(!0).animate(e,m,l.animationEasing,function(){f(c,i)}):(h.css(e),f(c,i))}function f(i,t){function o(i,t){var o,s;return i.length&&t?(o=i.offset().left,s=i.offset().top,{x:o-t.offset().left+i.outerWidth()/2,y:s-t.offset().top+i.outerHeight()/2}):null}var s,a,n,r,e,c,l,h=i.val(),d=i.attr("data-opacity"),p=i.parent(),u=i.data("minicolors-settings"),g=p.find(".minicolors-input-swatch"),m=p.find(".minicolors-grid"),f=p.find(".minicolors-slider"),v=p.find(".minicolors-opacity-slider"),b=m.find("[class$=-picker]"),w=f.find("[class$=-picker]"),y=v.find("[class$=-picker]"),C=o(b,m),k=o(w,f),M=o(y,v);if(t.is(".minicolors-grid, .minicolors-slider, .minicolors-opacity-slider")){switch(u.control){case"wheel":r=m.width()/2-C.x,e=m.height()/2-C.y,c=Math.sqrt(r*r+e*e),(l=Math.atan2(e,r))<0&&(l+=2*Math.PI),75<c&&(c=75,C.x=69-75*Math.cos(l),C.y=69-75*Math.sin(l)),a=F(c/.75,0,100),h=q({h:s=F(180*l/Math.PI,0,360),s:a,b:n=F(100-Math.floor(k.y*(100/f.height())),0,100)}),f.css("backgroundColor",q({h:s,s:a,b:100}));break;case"saturation":h=q({h:s=F(parseInt(C.x*(360/m.width()),10),0,360),s:a=F(100-Math.floor(k.y*(100/f.height())),0,100),b:n=F(100-Math.floor(C.y*(100/m.height())),0,100)}),f.css("backgroundColor",q({h:s,s:100,b:n})),p.find(".minicolors-grid-inner").css("opacity",a/100);break;case"brightness":h=q({h:s=F(parseInt(C.x*(360/m.width()),10),0,360),s:a=F(100-Math.floor(C.y*(100/m.height())),0,100),b:n=F(100-Math.floor(k.y*(100/f.height())),0,100)}),f.css("backgroundColor",q({h:s,s:a,b:100})),p.find(".minicolors-grid-inner").css("opacity",1-n/100);break;default:h=q({h:s=F(360-parseInt(k.y*(360/f.height()),10),0,360),s:a=F(Math.floor(C.x*(100/m.width())),0,100),b:n=F(100-Math.floor(C.y*(100/m.height())),0,100)}),m.css("backgroundColor",q({h:s,s:100,b:100}))}x(i,h,d=u.opacity?parseFloat(1-M.y/v.height()).toFixed(2):1)}else g.find("span").css({backgroundColor:h,opacity:String(d)}),S(i,h,d)}function x(i,t,o){var s,a=i.parent(),n=i.data("minicolors-settings"),r=a.find(".minicolors-input-swatch");n.opacity&&i.attr("data-opacity",o),t="rgb"===n.format?(s=T(t)?I(t,!0):L(M(t,!0)),o=""===i.attr("data-opacity")?1:F(parseFloat(i.attr("data-opacity")).toFixed(2),0,1),!isNaN(o)&&n.opacity||(o=1),i.minicolors("rgbObject").a<=1&&s&&n.opacity?"rgba("+s.r+", "+s.g+", "+s.b+", "+parseFloat(o)+")":"rgb("+s.r+", "+s.g+", "+s.b+")"):(T(t)&&(t=j(t)),k(t,n.letterCase)),i.val(t),r.find("span").css({backgroundColor:t,opacity:String(o)}),S(i,t,o)}function d(i,t){var o,s,a,n,r,e,c,l,h,d,p=i.parent(),u=i.data("minicolors-settings"),g=p.find(".minicolors-input-swatch"),m=p.find(".minicolors-grid"),f=p.find(".minicolors-slider"),v=p.find(".minicolors-opacity-slider"),b=m.find("[class$=-picker]"),w=f.find("[class$=-picker]"),y=v.find("[class$=-picker]");switch(T(i.val())?(o=j(i.val()),(r=F(parseFloat(D(i.val())).toFixed(2),0,1))&&i.attr("data-opacity",r)):o=k(M(i.val(),!0),u.letterCase),s=function(i){var t=function(i){var t={h:0,s:0,b:0},o=Math.min(i.r,i.g,i.b),s=Math.max(i.r,i.g,i.b),a=s-o;t.b=s,t.s=0!==s?255*a/s:0,0!==t.s?i.r===s?t.h=(i.g-i.b)/a:i.g===s?t.h=2+(i.b-i.r)/a:t.h=4+(i.r-i.g)/a:t.h=-1;t.h*=60,t.h<0&&(t.h+=360);return t.s*=100/255,t.b*=100/255,t}(L(i));0===t.s&&(t.h=360);return t}(o=o||k(z(u.defaultValue,!0),u.letterCase)),n=u.keywords?C.map(u.keywords.split(","),function(i){return i.toLowerCase().trim()}):[],e=""!==i.val()&&-1<C.inArray(i.val().toLowerCase(),n)?k(i.val()):T(i.val())?I(i.val()):o,t||i.val(e),u.opacity&&(a=""===i.attr("data-opacity")?1:F(parseFloat(i.attr("data-opacity")).toFixed(2),0,1),isNaN(a)&&(a=1),i.attr("data-opacity",a),g.find("span").css("opacity",String(a)),l=F(v.height()-v.height()*a,0,v.height()),y.css("top",l+"px")),"transparent"===i.val().toLowerCase()&&g.find("span").css("opacity",String(0)),g.find("span").css("backgroundColor",o),u.control){case"wheel":h=F(Math.ceil(.75*s.s),0,m.height()/2),d=s.h*Math.PI/180,c=F(75-Math.cos(d)*h,0,m.width()),l=F(75-Math.sin(d)*h,0,m.height()),b.css({top:l+"px",left:c+"px"}),l=150-s.b/(100/m.height()),""===o&&(l=0),w.css("top",l+"px"),f.css("backgroundColor",q({h:s.h,s:s.s,b:100}));break;case"saturation":c=F(5*s.h/12,0,150),l=F(m.height()-Math.ceil(s.b/(100/m.height())),0,m.height()),b.css({top:l+"px",left:c+"px"}),l=F(f.height()-s.s*(f.height()/100),0,f.height()),w.css("top",l+"px"),f.css("backgroundColor",q({h:s.h,s:100,b:s.b})),p.find(".minicolors-grid-inner").css("opacity",s.s/100);break;case"brightness":c=F(5*s.h/12,0,150),l=F(m.height()-Math.ceil(s.s/(100/m.height())),0,m.height()),b.css({top:l+"px",left:c+"px"}),l=F(f.height()-s.b*(f.height()/100),0,f.height()),w.css("top",l+"px"),f.css("backgroundColor",q({h:s.h,s:s.s,b:100})),p.find(".minicolors-grid-inner").css("opacity",1-s.b/100);break;default:c=F(Math.ceil(s.s/(100/m.width())),0,m.width()),l=F(m.height()-Math.ceil(s.b/(100/m.height())),0,m.height()),b.css({top:l+"px",left:c+"px"}),l=F(f.height()-s.h/(360/f.height()),0,f.height()),w.css("top",l+"px"),m.css("backgroundColor",q({h:s.h,s:100,b:100}))}i.data("minicolors-initialized")&&S(i,e,a)}function S(i,t,o){var s,a,n,r=i.data("minicolors-settings"),e=i.data("minicolors-lastChange");if(!e||e.value!==t||e.opacity!==o){if(i.data("minicolors-lastChange",{value:t,opacity:o}),r.swatches&&0!==r.swatches.length){for(s=T(t)?I(t,!0):L(t),a=-1,n=0;n<r.swatches.length;++n)if(s.r===r.swatches[n].r&&s.g===r.swatches[n].g&&s.b===r.swatches[n].b&&s.a===r.swatches[n].a){a=n;break}i.parent().find(".minicolors-swatches .minicolors-swatch").removeClass("selected"),-1!==a&&i.parent().find(".minicolors-swatches .minicolors-swatch").eq(n).addClass("selected")}r.change&&(r.changeDelay?(clearTimeout(i.data("minicolors-changeTimeout")),i.data("minicolors-changeTimeout",setTimeout(function(){r.change.call(i.get(0),t,o)},r.changeDelay))):r.change.call(i.get(0),t,o)),i.trigger("change").trigger("input")}}function k(i,t){return"uppercase"===t?i.toUpperCase():i.toLowerCase()}function M(i,t){return!(i=i.replace(/^#/g,"")).match(/^[A-F0-9]{3,6}/gi)||3!==i.length&&6!==i.length?"":(3===i.length&&t&&(i=i[0]+i[0]+i[1]+i[1]+i[2]+i[2]),"#"+i)}function I(i,t){var o=i.replace(/[^\d,.]/g,"").split(",");return o[0]=F(parseInt(o[0],10),0,255),o[1]=F(parseInt(o[1],10),0,255),o[2]=F(parseInt(o[2],10),0,255),void 0!==o[3]&&(o[3]=F(parseFloat(o[3],10),0,1)),t?void 0!==o[3]?{r:o[0],g:o[1],b:o[2],a:o[3]}:{r:o[0],g:o[1],b:o[2]}:void 0!==o[3]&&o[3]<=1?"rgba("+o[0]+", "+o[1]+", "+o[2]+", "+o[3]+")":"rgb("+o[0]+", "+o[1]+", "+o[2]+")"}function z(i,t){return T(i)?I(i):M(i,t)}function F(i,t,o){return i<t&&(i=t),o<i&&(i=o),i}function T(i){var t=i.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);return!(!t||4!==t.length)}function D(i){return(i=i.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+(\.\d{1,2})?|\.\d{1,2})[\s+]?/i))&&6===i.length?i[4]:"1"}function j(i){return(i=i.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i))&&4===i.length?"#"+("0"+parseInt(i[1],10).toString(16)).slice(-2)+("0"+parseInt(i[2],10).toString(16)).slice(-2)+("0"+parseInt(i[3],10).toString(16)).slice(-2):""}function p(i){var o=[i.r.toString(16),i.g.toString(16),i.b.toString(16)];return C.each(o,function(i,t){1===t.length&&(o[i]="0"+t)}),"#"+o.join("")}function q(i){return p((t=i,n={},r=Math.round(t.h),e=Math.round(255*t.s/100),c=Math.round(255*t.b/100),0===e?n.r=n.g=n.b=c:(a=r%60*((o=c)-(s=(255-e)*c/255))/60,360===r&&(r=0),r<60?(n.r=o,n.b=s,n.g=s+a):r<120?(n.g=o,n.b=s,n.r=o-a):r<180?(n.g=o,n.r=s,n.b=s+a):r<240?(n.b=o,n.r=s,n.g=o-a):r<300?(n.b=o,n.g=s,n.r=s+a):r<360?(n.r=o,n.g=s,n.b=o-a):(n.r=0,n.g=0,n.b=0)),{r:Math.round(n.r),g:Math.round(n.g),b:Math.round(n.b)}));var t,o,s,a,n,r,e,c}function L(i){return{r:(i=parseInt(-1<i.indexOf("#")?i.substring(1):i,16))>>16,g:(65280&i)>>8,b:255&i}}C.minicolors={defaults:{animationSpeed:50,animationEasing:"swing",change:null,changeDelay:0,control:"hue",defaultValue:"",format:"hex",hide:null,hideSpeed:100,inline:!1,keywords:"",letterCase:"lowercase",opacity:!1,position:"bottom",show:null,showSpeed:100,theme:"default",swatches:[]}},C.extend(C.fn,{minicolors:function(i,t){switch(i){case"destroy":return C(this).each(function(){o(C(this))}),C(this);case"hide":return a(),C(this);case"opacity":return void 0===t?C(this).attr("data-opacity"):(C(this).each(function(){d(C(this).attr("data-opacity",t))}),C(this));case"rgbObject":return function(i){var t,o=C(i).attr("data-opacity");{var s;t=T(C(i).val())?I(C(i).val(),!0):(s=M(C(i).val(),!0),L(s))}if(!t)return null;void 0!==o&&C.extend(t,{a:parseFloat(o)});return t}(C(this));case"rgbString":case"rgbaString":return function(i,t){var o,s=C(i).attr("data-opacity");{var a;o=T(C(i).val())?I(C(i).val(),!0):(a=M(C(i).val(),!0),L(a))}if(!o)return null;void 0===s&&(s=1);return t?"rgba("+o.r+", "+o.g+", "+o.b+", "+parseFloat(s)+")":"rgb("+o.r+", "+o.g+", "+o.b+")"}(C(this),"rgbaString"===i);case"settings":return void 0===t?C(this).data("minicolors-settings"):(C(this).each(function(){var i=C(this).data("minicolors-settings")||{};o(C(this)),C(this).minicolors(C.extend(!0,i,t))}),C(this));case"show":return s(C(this).eq(0)),C(this);case"value":return void 0===t?C(this).val():(C(this).each(function(){"object"==typeof t&&null!==t?(void 0!==t.opacity&&C(this).attr("data-opacity",F(t.opacity,0,1)),t.color&&C(this).val(t.color)):C(this).val(t),d(C(this))}),C(this));default:return"create"!==i&&(t=i),C(this).each(function(){!function(t,i){var o,s,a,n,r,e,c,l=C('<div class="minicolors" />'),h=C.minicolors.defaults;if(t.data("minicolors-initialized"))return;i=C.extend(!0,{},h,i),l.addClass("minicolors-theme-"+i.theme).toggleClass("minicolors-with-opacity",i.opacity),void 0!==i.position&&C.each(i.position.split(" "),function(){l.addClass("minicolors-position-"+this)});s="rgb"===i.format?i.opacity?"25":"20":i.keywords?"11":"7";t.addClass("minicolors-input").data("minicolors-initialized",!1).data("minicolors-settings",i).prop("size",s).wrap(l).after('<div class="minicolors-panel minicolors-slider-'+i.control+'"><div class="minicolors-slider minicolors-sprite"><div class="minicolors-picker"></div></div><div class="minicolors-opacity-slider minicolors-sprite"><div class="minicolors-picker"></div></div><div class="minicolors-grid minicolors-sprite"><div class="minicolors-grid-inner"></div><div class="minicolors-picker"><div></div></div></div></div>'),i.inline||(t.after('<span class="minicolors-swatch minicolors-sprite minicolors-input-swatch"><span class="minicolors-swatch-color"></span></span>'),t.next(".minicolors-input-swatch").on("click",function(i){i.preventDefault(),t.trigger("focus")}));if((e=t.parent().find(".minicolors-panel")).on("selectstart",function(){return!1}).end(),i.swatches&&0!==i.swatches.length)for(e.addClass("minicolors-with-swatches"),a=C('<ul class="minicolors-swatches"></ul>').appendTo(e),c=0;c<i.swatches.length;++c)n="object"==typeof i.swatches[c]?(o=i.swatches[c].name,i.swatches[c].color):(o="",i.swatches[c]),n=T(r=n)?I(n,!0):L(M(n,!0)),C('<li class="minicolors-swatch minicolors-sprite"><span class="minicolors-swatch-color"></span></li>').attr("title",o).appendTo(a).data("swatch-color",r).find(".minicolors-swatch-color").css({backgroundColor:"transparent"!==r?p(n):"transparent",opacity:String(n.a)}),i.swatches[c]=n;i.inline&&t.parent().addClass("minicolors-inline");d(t,!1),t.data("minicolors-initialized",!0)}(C(this),t)}),C(this)}}}),C([document]).on("mousedown.minicolors touchstart.minicolors",function(i){C(i.target).parents().add(i.target).hasClass("minicolors")||a()}).on("mousedown.minicolors touchstart.minicolors",".minicolors-grid, .minicolors-slider, .minicolors-opacity-slider",function(i){var t=C(this);i.preventDefault(),C(i.delegateTarget).data("minicolors-target",t),n(t,i,!0)}).on("mousemove.minicolors touchmove.minicolors",function(i){var t=C(i.delegateTarget).data("minicolors-target");t&&n(t,i)}).on("mouseup.minicolors touchend.minicolors",function(){C(this).removeData("minicolors-target")}).on("click.minicolors",".minicolors-swatches li",function(i){i.preventDefault();var t=C(this),o=t.parents(".minicolors").find(".minicolors-input"),s=t.data("swatch-color");x(o,s,D(s)),d(o)}).on("mousedown.minicolors touchstart.minicolors",".minicolors-input-swatch",function(i){var t=C(this).parent().find(".minicolors-input");i.preventDefault(),s(t)}).on("focus.minicolors",".minicolors-input",function(){var i=C(this);i.data("minicolors-initialized")&&s(i)}).on("blur.minicolors",".minicolors-input",function(){var i,t,o,s,a,n=C(this),r=n.data("minicolors-settings");n.data("minicolors-initialized")&&(i=r.keywords?C.map(r.keywords.split(","),function(i){return i.toLowerCase().trim()}):[],a=""!==n.val()&&-1<C.inArray(n.val().toLowerCase(),i)?n.val():null===(o=T(n.val())?I(n.val(),!0):(t=M(n.val(),!0))?L(t):null)?r.defaultValue:"rgb"===r.format?r.opacity?I("rgba("+o.r+","+o.g+","+o.b+","+n.attr("data-opacity")+")"):I("rgb("+o.r+","+o.g+","+o.b+")"):p(o),s=r.opacity?n.attr("data-opacity"):1,"transparent"===a.toLowerCase()&&(s=0),n.closest(".minicolors").find(".minicolors-input-swatch > span").css("opacity",String(s)),n.val(a),""===n.val()&&n.val(z(r.defaultValue,!0)),n.val(k(n.val(),r.letterCase)))}).on("keydown.minicolors",".minicolors-input",function(i){var t=C(this);if(t.data("minicolors-initialized"))switch(i.which){case 9:a();break;case 13:case 27:a(),t.blur()}}).on("keyup.minicolors",".minicolors-input",function(){var i=C(this);i.data("minicolors-initialized")&&d(i,!0)}).on("paste.minicolors",".minicolors-input",function(){var i=C(this);i.data("minicolors-initialized")&&setTimeout(function(){d(i,!0)},1)})});