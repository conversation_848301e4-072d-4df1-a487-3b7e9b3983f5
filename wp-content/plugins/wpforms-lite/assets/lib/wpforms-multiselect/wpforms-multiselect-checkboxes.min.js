!function(e,t){const s="WPFormsMultiSelectCheckbox";"function"==typeof define&&define.amd?define([],t(s)):"object"==typeof exports?module.exports=t(s):e[s]=t(s)}(this,(function(e){class t{static defaults={showSearch:!1,showTags:!1,showClear:!1,showMask:!1,tagsPlacement:"top",delimiter:", ",customOpener:null};static i18n={search:"Search",clear:"Clear",all:"All",multiple:"Multiple"};constructor(e,s={}){this.element=e,this.i18n=Object.assign({},t.i18n,s.i18n),s.i18n&&delete s.i18n,this.settings=Object.assign({},t.defaults,s),this.wrapper=document.createElement("div"),this.wrapInner=document.createElement("div"),this.formWrapper=document.createElement("div"),this.input=document.createElement("input"),this.list=document.createElement("div"),this.selectedOptionsContainer=null,this.clearButton=null,this.search=null,this.mask=null}init(){try{const e="string"==typeof this.element?document.getElementById(this.element):this.element;if(!e)throw new s("The specified element could not be found.");if(!(e instanceof HTMLSelectElement))throw new s("This plugin can only be used on select fields.");if(!e.multiple)throw new s('This plugin can only be used on select fields with the "multiple" attribute.');if(0===e.options.length)throw new s("This plugin can only be used on select fields with available options.");let t;try{t=JSON.parse(e.getAttribute("data-settings"))||{}}catch(e){throw new s("Failed to parse the plugin settings. Please ensure that the settings are provided in valid JSON format.")}if(this.i18n=Object.assign({},this.i18n,t.i18n),this.settings=Object.assign({},this.settings,t),"boolean"!=typeof this.settings.showSearch)throw new s('The "showSearch" property must be a boolean value.');if("boolean"!=typeof this.settings.showTags)throw new s('The "showTags" property must be a boolean value.');if("boolean"!=typeof this.settings.showClear)throw new s('The "showClear" property must be a boolean value.');if("string"!=typeof this.settings.tagsPlacement||!["top","bottom","left","right"].includes(this.settings.tagsPlacement))throw new s('The "tagsPlacement" property must be a string value and one of the following: top, bottom, left, right.');i.call(this,e)}catch(e){n(e)}}update(e,t){try{if(!e)throw new s('The update method requires the "element" parameter.');if(!(e instanceof HTMLSelectElement))throw new s("This plugin can only be used on select fields.");if(!Array.isArray(t))throw new s('The update method requires the "values" parameter to be an array.');const i=Array.from(e.parentNode.querySelectorAll('input[type="checkbox"]'));if(0===i.length)throw new s("Could not find the checkboxes associated with the select field.");i.forEach((e=>{e.checked=t.includes(e.value)}));const n=e.parentNode.querySelector(".wpforms-multiselect-checkbox-list");if(!n)throw new s("Could not find the list element associated with the select field.");n.dispatchEvent(new CustomEvent("change",{detail:{isForced:!0}}))}catch(e){n(e)}}}class s extends Error{constructor(t){super(t),this.name=e}}function i(e){e.style.display="none";const{showSearch:s,showTags:i,showClear:n,showMask:a,tagsPlacement:l,delimiter:o}=this.settings;this.wrapper.classList.add("wpforms-multiselect-checkbox-dropdown"),e.parentNode.insertBefore(this.wrapper,e),this.wrapper.appendChild(e),this.wrapInner.classList.add("wpforms-multiselect-checkbox-wrapper"),this.wrapper.appendChild(this.wrapInner),this.formWrapper.classList.add("wpforms-multiselect-checkbox-form-outline"),this.wrapInner.appendChild(this.formWrapper),a&&(this.wrapper.classList.add("has-mask"),this.mask=document.createElement("span"),this.mask.classList.add("wpforms-multiselect-checkbox-input-mask"),this.formWrapper.appendChild(this.mask));const r=e.getAttribute("placeholder")||"";Object.assign(this.input,{type:"text",name:e.name,disabled:e.disabled,placeholder:r,className:"wpforms-multiselect-checkbox-input",readOnly:!0,autocomplete:"off",autocapitalize:"none",spellcheck:!1,role:"combobox",tabIndex:0,ariaAutoComplete:"list",ariaHasPopup:!0,ariaExpanded:!1,ariaMultiSelectable:!0,ariaReadOnly:!0,ariaDisabled:e.disabled}),this.formWrapper.appendChild(this.input),e.removeAttribute("name");const c=document.createElement("span");Object.assign(c,{className:"wpforms-multiselect-checkbox-input-placeholder",role:"presentation"}),c.textContent=r,this.formWrapper.appendChild(c);const h=document.createElement("span");if(h.setAttribute("role","presentation"),h.classList.add("wpforms-multiselect-checkbox-arrow"),this.formWrapper.appendChild(h),e.disabled)return void this.formWrapper.classList.add("disabled");n&&(this.wrapper.classList.add("has-clear"),this.clearButton=document.createElement("span"),Object.assign(this.clearButton,{innerHTML:"&times;",className:"wpforms-multiselect-checkbox-clear",role:"button",ariaLabel:t.i18n.clear}),this.formWrapper.appendChild(this.clearButton)),this.list.classList.add("wpforms-multiselect-checkbox-list"),this.wrapInner.appendChild(this.list),i&&(this.selectedOptionsContainer=document.createElement("div"),this.selectedOptionsContainer.classList.add("wpforms-multiselect-checkbox-selected"),this.wrapper.classList.add(`has-tags-${l}`),this.wrapper.appendChild(this.selectedOptionsContainer)),s&&(this.wrapper.classList.add("has-search"),this.search=document.createElement("input"),Object.assign(this.search,{type:"search",className:"wpforms-multiselect-checkbox-search",placeholder:t.i18n.search,ariaLabel:t.i18n.search}),this.list.appendChild(this.search));const p=document.createElement("div");Object.assign(p,{role:"listbox",className:"wpforms-multiselect-checkbox-items",ariaLabel:this.input.getAttribute("placeholder"),ariaHidden:!1}),this.list.appendChild(p);let d=null;const u=Array.from(e.options).map((e=>{const t=document.createElement("label"),s=document.createElement("span"),i=document.createElement("input"),n="OPTGROUP"===e.parentNode.tagName?e.parentNode.label:null;if(n&&n!==d){const e=document.createElement("span");e.classList.add("wpforms-multiselect-checkbox-optgroup"),e.textContent=n,p.appendChild(e),d=n}return s.textContent=e.textContent,t.classList.toggle("disabled",e.disabled),t.setAttribute("role","option"),p.appendChild(t),Object.assign(i,{type:"checkbox",disabled:e.disabled,value:e.value,checked:e.selected,ariaDisabled:e.disabled}),t.appendChild(i),t.appendChild(s),i})),m=()=>{const t=Array.from(u).filter((e=>e.checked)).map((e=>e.value));if(this.input.value=t.join(o),this.input.setAttribute("value",this.input.value),i){if(this.selectedOptionsContainer.textContent="",0===t.length)return this.input.value="",this.input.removeAttribute("value"),void(this.clearButton&&(this.clearButton.style.visibility="hidden"));t.forEach((e=>{const t=document.createElement("span");t.classList.add("wpforms-multiselect-checkbox-selected-option"),t.textContent=e;const s=document.createElement("span");s.dataset.value=e,Object.assign(s,{innerHTML:"&times;",className:"wpforms-multiselect-checkbox-remove",role:"button",ariaLabel:`Remove ${e}`}),t.appendChild(s),this.selectedOptionsContainer.appendChild(t)}))}a&&(t.length===u.length&&u.length>1?(c.textContent="",this.mask.textContent=this.i18n.all):0===t.length||1===t.length?(this.mask.textContent=b(t[0]),c.textContent=r):(c.textContent="",this.mask.textContent=this.i18n.multiple.replace("{count}",t.length))),this.clearButton&&(this.clearButton.style.visibility=t.length?"visible":"hidden");const s=new CustomEvent("wpforms_multiselect_checkbox_changed",{bubbles:!0,cancelable:!0,detail:{selectedOptions:t}});e.dispatchEvent(s)},b=t=>{const s=Array.from(e.options).find((e=>e.value===t));return s?s.textContent.trim():""},f=t=>{const s=this.list.querySelector(`input[value="${t}"]`);if(!s)return;s.checked=!1,m();const i=new CustomEvent("wpforms_multiselect_checkbox_removed",{bubbles:!0,cancelable:!0,detail:{removedOption:t}});e.dispatchEvent(i)},w=({target:e})=>{const t=this.list.classList.toggle("open");if(e.setAttribute("aria-expanded",t),x(t),!t)return k(),void this.list.classList.remove("open-up");const s=this.list.getBoundingClientRect(),i=window.innerHeight||document.documentElement.clientHeight;s.top+s.height>i?this.list.classList.add("open-up"):this.list.classList.remove("open-up")},g=()=>{u.forEach((e=>{e.checked=!1})),m(),y({},!0)},v=({target:e})=>{const t=e.value.toLowerCase();Array.from(this.list.querySelectorAll("label")).forEach((e=>{const s=e.querySelector("span").textContent.toLowerCase();e.style.display=s.includes(t)||0===t.length?"inline-flex":"none"}))},C=({target:e})=>{if(!e.classList.contains("wpforms-multiselect-checkbox-remove"))return;const t=e.dataset.value;f(t)},y=({target:e},t=!1)=>{this.wrapper.contains(e)&&!t||this.settings.customOpener&&this.settings.customOpener?.contains(e)||(k(),this.list.classList.remove("open","open-up"),this.input.setAttribute("aria-expanded",!1),x(!1))},k=()=>{this.search&&(this.search.value="",this.search.dispatchEvent(new Event("input")))},x=t=>{const s=new CustomEvent("wpforms_multiselect_checkbox_list_toggle",{bubbles:!0,detail:{isOpen:t}});e.dispatchEvent(s)};this.input.addEventListener("click",w),this.input.addEventListener("keydown",(e=>{" "!==e.key&&"Spacebar"!==e.key||(e.preventDefault(),w(e))})),this.list.addEventListener("change",(({target:e,detail:t})=>{"INPUT"!==e.tagName&&"checkbox"!==e.type&&!0!==t?.isForced||m()})),document.addEventListener("click",y),this.settings.customOpener&&this.settings.customOpener.addEventListener("click",w),this.selectedOptionsContainer&&this.selectedOptionsContainer.addEventListener("click",C),this.clearButton&&this.clearButton.addEventListener("click",g),this.search&&this.search.addEventListener("input",function(e,t){let s;return function(...i){clearTimeout(s),s=setTimeout((()=>{e.apply(this,i)}),t)}}(v,300)),m()}function n(e){console.error(e)}return t}));
