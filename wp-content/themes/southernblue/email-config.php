<?php
/**
 * Email Configuration for Hotel Southern Blue
 * 
 * IMPORTANT: Configure your Gmail SMTP settings here
 * 
 * To set up Gmail SMTP:
 * 1. Go to your Google Account settings
 * 2. Enable 2-Factor Authentication
 * 3. Generate an App Password for "Mail"
 * 4. Use that App Password below (not your regular password)
 */

// Email Configuration
define('HOTEL_SMTP_HOST', 'mail.hotelsouthernblue.co.ke');
define('HOTEL_SMTP_PORT', 465);
define('HOTEL_SMTP_SECURE', 'ssl');
define('HOTEL_SMTP_USERNAME', '<EMAIL>');
define('HOTEL_SMTP_PASSWORD', 'Aug205/HSB'); // Gmail password

// Email Settings
define('HOTEL_FROM_EMAIL', '<EMAIL>');
define('HOTEL_FROM_NAME', 'Hotel Southern Blue');
define('HOTEL_ADMIN_EMAIL', '<EMAIL>');

// Enable/Disable SMTP
define('HOTEL_USE_SMTP', true);

/**
 * Instructions to get Gmail App Password:
 * 
 * 1. Go to https://myaccount.google.com/
 * 2. Click "Security" in the left sidebar
 * 3. Under "Signing in to Google", click "2-Step Verification"
 * 4. Enable 2-Step Verification if not already enabled
 * 5. Go back to Security page
 * 6. Under "Signing in to Google", click "App passwords"
 * 7. Select "Mail" and generate password
 * 8. Copy the 16-character password and paste it above
 * 
 * Example: abcd efgh ijkl mnop (remove spaces when pasting)
 */
