<?php
if (!defined('ABSPATH')) exit;

// Load Environment Handler FIRST
require_once get_template_directory() . '/includes/env-handler.php';

// Environment-based Asset URL Function
function get_asset_url($path) {
    return asset_url($path);
}

// Additional helper functions for specific asset types
function get_room_image_url($room_type, $image_name) {
    return room_image_url($room_type, $image_name);
}

function get_gallery_image_url($image_name) {
    return gallery_image_url($image_name);
}

function get_menu_url($filename = 'menu.pdf') {
    return menu_url($filename);
}

function get_theme_asset_url($path) {
    return theme_asset_url($path);
}

// Include required files
require_once get_template_directory() . '/inc/class-bootstrap-5-nav-walker.php';
require_once get_template_directory() . '/inc/booking-functions.php';

// --- SMTP Configuration for Hotel Southern Blue ---
// Configure PHPMailer to use Gmail SMTP for all outgoing mail
function sb_configure_smtp($phpmailer) {
    $phpmailer->isSMTP();
    $phpmailer->Host = 'mail.hotelsouthernblue.co.ke';
    $phpmailer->SMTPAuth = true;
    $phpmailer->Port = 465;
    $phpmailer->SMTPSecure = 'ssl';
    $phpmailer->Username = '<EMAIL>';
    $phpmailer->Password = 'Aug205/HSB';
    $phpmailer->setFrom('<EMAIL>', 'Hotel Southern Blue');
}
add_action('phpmailer_init', 'sb_configure_smtp');

// Add SMTP settings page
function add_smtp_settings_page() {
    add_options_page(
        'Email Settings',
        'Email Settings',
        'manage_options',
        'email-settings',
        'display_smtp_settings_page'
    );
}
add_action('admin_menu', 'add_smtp_settings_page');

// Display SMTP settings page
function display_smtp_settings_page() {
    if (isset($_POST['submit'])) {
        update_option('sb_use_smtp', isset($_POST['use_smtp']));
        update_option('sb_smtp_host', sanitize_text_field($_POST['smtp_host']));
        update_option('sb_smtp_port', intval($_POST['smtp_port']));
        update_option('sb_smtp_secure', sanitize_text_field($_POST['smtp_secure']));
        update_option('sb_smtp_username', sanitize_text_field($_POST['smtp_username']));
        update_option('sb_smtp_password', sanitize_text_field($_POST['smtp_password']));

        // Also update the email config file
        update_email_config_file($_POST);

        echo '<div class="notice notice-success"><p>Settings saved! Email configuration updated.</p></div>';
    }

    $use_smtp = get_option('sb_use_smtp', true);
    $smtp_host = get_option('sb_smtp_host', 'mail.hotelsouthernblue.co.ke');
    $smtp_port = get_option('sb_smtp_port', 465);
    $smtp_secure = get_option('sb_smtp_secure', 'ssl');
    $smtp_username = get_option('sb_smtp_username', '<EMAIL>');
    $smtp_password = get_option('sb_smtp_password', 'Aug205/HSB');

    // Set default values if not configured
    if (empty($smtp_username)) {
        update_option('sb_use_smtp', true);
        update_option('sb_smtp_host', 'mail.hotelsouthernblue.co.ke');
        update_option('sb_smtp_port', 465);
        update_option('sb_smtp_secure', 'ssl');
        update_option('sb_smtp_username', '<EMAIL>');
        update_option('sb_smtp_password', 'Aug205/HSB');

        $use_smtp = true;
        $smtp_host = 'mail.hotelsouthernblue.co.ke';
        $smtp_port = 465;
        $smtp_secure = 'ssl';
        $smtp_username = '<EMAIL>';
        $smtp_password = 'Aug205/HSB';
    }

    ?>
    <div class="wrap">
        <h1>Email Settings</h1>
        <form method="post" action="">
            <table class="form-table">
                <tr>
                    <th scope="row">Use SMTP</th>
                    <td>
                        <input type="checkbox" name="use_smtp" value="1" <?php checked($use_smtp); ?> />
                        <label>Enable SMTP for email delivery</label>
                    </td>
                </tr>
                <tr>
                    <th scope="row">SMTP Host</th>
                    <td><input type="text" name="smtp_host" value="<?php echo esc_attr($smtp_host); ?>" class="regular-text" /></td>
                </tr>
                <tr>
                    <th scope="row">SMTP Port</th>
                    <td><input type="number" name="smtp_port" value="<?php echo esc_attr($smtp_port); ?>" /></td>
                </tr>
                <tr>
                    <th scope="row">Encryption</th>
                    <td>
                        <select name="smtp_secure">
                            <option value="tls" <?php selected($smtp_secure, 'tls'); ?>>TLS</option>
                            <option value="ssl" <?php selected($smtp_secure, 'ssl'); ?>>SSL</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Username</th>
                    <td><input type="text" name="smtp_username" value="<?php echo esc_attr($smtp_username); ?>" class="regular-text" /></td>
                </tr>
                <tr>
                    <th scope="row">Password</th>
                    <td><input type="password" name="smtp_password" value="<?php echo esc_attr($smtp_password); ?>" class="regular-text" /></td>
                </tr>
            </table>

            <div class="notice notice-info">
                <p><strong>Gmail SMTP Setup:</strong></p>
                <ul>
                    <li>Host: smtp.gmail.com</li>
                    <li>Port: 587</li>
                    <li>Encryption: TLS</li>
                    <li>Username: <EMAIL></li>
                    <li>Password: Use an App Password (not your regular password)</li>
                </ul>
                <p><a href="https://support.google.com/accounts/answer/185833" target="_blank">How to create Gmail App Password</a></p>
            </div>

            <?php submit_button(); ?>
        </form>

        <div class="notice notice-warning">
            <h3>🚨 EMAIL SETUP OPTIONS</h3>
            <p><strong>Choose one of these methods to enable email sending:</strong></p>

            <h4>Option 1: Gmail with Less Secure Apps (Easiest)</h4>
            <ol>
                <li>Go to <a href="https://myaccount.google.com/security" target="_blank">Google Account Security</a></li>
                <li>Find "Less secure app access" and turn it ON</li>
                <li>Use your regular Gmail password in the settings above</li>
            </ol>

            <h4>Option 2: Create New Gmail Account</h4>
            <ol>
                <li>Create a new Gmail account (e.g., <EMAIL>)</li>
                <li>Enable "Less secure app access" on the new account</li>
                <li>Use the new account credentials above</li>
                <li>Set up email <NAME_EMAIL></li>
            </ol>

            <h4>Option 3: Use Alternative Email Service</h4>
            <p>Configure with Outlook.com, Yahoo, or other email providers that support SMTP.</p>

            <p><strong>Without email configuration, booking notifications will only be logged (not sent).</strong></p>
        </div>

        <h2>Test Email</h2>
        <p><a href="<?php echo home_url('/?test_email=true'); ?>" class="button" target="_blank">Send Test Email</a></p>
        <p><a href="<?php echo admin_url('tools.php?page=email-logs'); ?>" class="button">View Email Logs</a></p>

        <h2>Current Status</h2>
        <?php
        $smtp_configured = !empty(get_option('sb_smtp_password')) && get_option('sb_smtp_password') !== 'temp-password-here';
        if ($smtp_configured) {
            echo '<div class="notice notice-success"><p>✅ SMTP is configured. Emails should be working!</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>❌ Gmail App Password not configured. Emails will NOT be sent.</p></div>';
        }
        ?>
    </div>
    <?php
}

function southernblue_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    add_theme_support('customize-selective-refresh-widgets');
    
    // Register menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'southernblue'),
        'footer' => __('Footer Menu', 'southernblue')
    ));
}
add_action('after_setup_theme', 'southernblue_setup');

// Enqueue scripts and styles
function southernblue_enqueue_scripts() {
    // Enqueue Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', array(), null);
    // Enqueue Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0');
    // Enqueue Bootstrap CSS (5.3.0)
    wp_enqueue_style('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css', array(), '5.3.0');
    // Enqueue theme's main stylesheet
    wp_enqueue_style('southernblue-style', get_template_directory_uri() . '/css/main.css', array('bootstrap'), '1.0.0');
    // Enqueue Bootstrap JS (5.3.0) and Popper.js
    wp_enqueue_script('popper', 'https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js', array(), '2.11.6', true);
    wp_enqueue_script('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js', array('popper'), '5.3.0', true);
    // Enqueue custom JavaScript for gallery modal
    wp_enqueue_script('southernblue-gallery', get_template_directory_uri() . '/js/gallery.js', array('jquery'), '1.0.0', true);
}
add_action('wp_enqueue_scripts', 'southernblue_enqueue_scripts');

// Custom post type for Rooms
function create_room_post_type() {
    register_post_type('room',
        array(
            'labels' => array(
                'name' => __('Rooms'),
                'singular_name' => __('Room')
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
            'menu_icon' => 'dashicons-building',
            'rewrite' => array('slug' => 'rooms')
        )
    );
}
add_action('init', 'create_room_post_type');

// Add custom meta boxes for room details
function add_room_meta_boxes() {
    add_meta_box(
        'room_details',
        __('Room Details', 'southernblue'),
        'room_details_callback',
        'room',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_room_meta_boxes');

function room_details_callback($post) {
    wp_nonce_field('room_details_nonce', 'room_details_nonce');
    $price = get_post_meta($post->ID, '_room_price', true);
    $capacity = get_post_meta($post->ID, '_room_capacity', true);
    ?>
    <p>
        <label for="room_price"><?php _e('Price per night:', 'southernblue'); ?></label>
        <input type="number" id="room_price" name="room_price" value="<?php echo esc_attr($price); ?>" step="0.01">
    </p>
    <p>
        <label for="room_capacity"><?php _e('Room Capacity:', 'southernblue'); ?></label>
        <input type="number" id="room_capacity" name="room_capacity" value="<?php echo esc_attr($capacity); ?>">
    </p>
    <?php
}

// Save room details
function save_room_details($post_id) {
    if (!isset($_POST['room_details_nonce']) || !wp_verify_nonce($_POST['room_details_nonce'], 'room_details_nonce'))
        return;
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE)
        return;
    
    if (isset($_POST['room_price']))
        update_post_meta($post_id, '_room_price', sanitize_text_field($_POST['room_price']));
    
    if (isset($_POST['room_capacity']))
        update_post_meta($post_id, '_room_capacity', sanitize_text_field($_POST['room_capacity']));
}
add_action('save_post', 'save_room_details');

// Create necessary pages on theme activation
function southernblue_create_pages() {
    $pages = array(
        'book-now' => array(
            'title' => 'Book Now',
            'template' => 'page-book-now.php'
        ),
        'booking-confirmation' => array(
            'title' => 'Booking Confirmation',
            'template' => 'page-booking-confirmation.php'
        )
    );

    foreach ($pages as $slug => $page) {
        $existing_page = get_page_by_path($slug);
        if (!$existing_page) {
            $page_id = wp_insert_post(array(
                'post_title' => $page['title'],
                'post_name' => $slug,
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_content' => '',
            ));
            
            if ($page_id && !is_wp_error($page_id)) {
                update_post_meta($page_id, '_wp_page_template', $page['template']);
            }
        }
    }
}
add_action('after_switch_theme', 'southernblue_create_pages');

// Create booking tables on theme activation
function sb_create_booking_tables() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();

    // Rooms table
    $rooms_table = $wpdb->prefix . 'sb_rooms';
    $sql_rooms = "CREATE TABLE IF NOT EXISTS $rooms_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        room_number varchar(20) NOT NULL,
        room_type varchar(50) NOT NULL,
        price decimal(10,2) NOT NULL,
        capacity int NOT NULL,
        description text NOT NULL,
        amenities text NOT NULL,
        status varchar(20) DEFAULT 'available',
        PRIMARY KEY  (id)
    ) $charset_collate;";

    // Bookings table
    $bookings_table = $wpdb->prefix . 'sb_bookings';
    $sql_bookings = "CREATE TABLE IF NOT EXISTS $bookings_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        room_id mediumint(9) NOT NULL,
        customer_name varchar(100) NOT NULL,
        customer_email varchar(100) NOT NULL,
        customer_phone varchar(20) NOT NULL,
        check_in date NOT NULL,
        check_out date NOT NULL,
        guests int NOT NULL,
        total_price decimal(10,2) NOT NULL,
        booking_status varchar(20) DEFAULT 'pending',
        payment_status varchar(20) DEFAULT 'unpaid',
        booking_date timestamp DEFAULT CURRENT_TIMESTAMP,
        special_requests text,
        PRIMARY KEY  (id)
    ) $charset_collate;";

    // Payments table
    $payments_table = $wpdb->prefix . 'sb_payments';
    $sql_payments = "CREATE TABLE IF NOT EXISTS $payments_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        booking_id mediumint(9) NOT NULL,
        amount decimal(10,2) NOT NULL,
        payment_method varchar(50) NOT NULL,
        transaction_id varchar(100) NOT NULL,
        payment_date timestamp DEFAULT CURRENT_TIMESTAMP,
        status varchar(20) DEFAULT 'completed',
        PRIMARY KEY  (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql_rooms);
    dbDelta($sql_bookings);
    dbDelta($sql_payments);
}
add_action('after_switch_theme', 'sb_create_booking_tables');

// Add booking management menu to admin
function sb_add_booking_menu() {
    add_menu_page(
        'Booking Management',
        'Bookings',
        'manage_options',
        'sb-bookings',
        'sb_booking_management_page',
        'dashicons-calendar-alt',
        20
    );
}
add_action('admin_menu', 'sb_add_booking_menu');

// Booking management page
function sb_booking_management_page() {
    include(get_template_directory() . '/admin/booking-management.php');
}

// AJAX handler for checking room availability
function sb_check_room_availability() {
    if (!isset($_POST['check_in']) || !isset($_POST['check_out']) || !isset($_POST['room_type'])) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    global $wpdb;
    $check_in = sanitize_text_field($_POST['check_in']);
    $check_out = sanitize_text_field($_POST['check_out']);
    $room_type = sanitize_text_field($_POST['room_type']);

    $available_rooms = $wpdb->get_results($wpdb->prepare(
        "SELECT r.* FROM {$wpdb->prefix}sb_rooms r 
        WHERE r.room_type = %s 
        AND r.status = 'available'
        AND r.id NOT IN (
            SELECT b.room_id FROM {$wpdb->prefix}sb_bookings b
            WHERE (b.check_in <= %s AND b.check_out >= %s)
            OR (b.check_in <= %s AND b.check_out >= %s)
            OR (b.check_in >= %s AND b.check_out <= %s)
        )",
        $room_type, $check_out, $check_in, $check_out, $check_in, $check_in, $check_out
    ));

    wp_send_json_success(['available_rooms' => $available_rooms]);
}
add_action('wp_ajax_check_room_availability', 'sb_check_room_availability');
add_action('wp_ajax_nopriv_check_room_availability', 'sb_check_room_availability');

// Email notifications for bookings
function sb_send_booking_emails($booking_id_or_data, $booking_data = null) {
    global $wpdb;
    $booking = null;
    if (is_array($booking_id_or_data) && $booking_data === null) {
        $booking = (object)$booking_id_or_data;
        $booking_id = null;
    } else if ($booking_data !== null) {
        $booking = (object)$booking_data;
        $booking_id = $booking_id_or_data;
    } else {
        $booking_id = $booking_id_or_data;
        $booking = $wpdb->get_row($wpdb->prepare(
            "SELECT b.*, r.room_number, r.room_type, r.price 
            FROM {$wpdb->prefix}sb_bookings b
            LEFT JOIN {$wpdb->prefix}sb_rooms r ON b.room_id = r.id
            WHERE b.id = %d",
            $booking_id
        ));
        if (!$booking) {
            return false;
        }
    }

    // --- Direct email notification (like contact form) ---
    // Always send admin notifications to the hotel address, not the WP admin email
    $admin_email = '<EMAIL>';
    $hotel_name = 'Hotel Southern Blue';
    $customer_email = $booking->customer_email ?? '';
    $customer_name = $booking->customer_name ?? '';
    $room_type = $booking->room_type ?? '';
    $room_number = $booking->room_number ?? '';
    $check_in = $booking->check_in ?? '';
    $check_out = $booking->check_out ?? '';
    $guests = $booking->guests ?? '';
    $total_price = $booking->total_price ?? '';
    $special_requests = $booking->special_requests ?? '';
    $customer_phone = $booking->customer_phone ?? '';

    $subject_admin = 'New Booking Received';
    $message_admin = "A new booking has been received:\n\n"
        . "Name: $customer_name\n"
        . "Email: $customer_email\n"
        . "Phone: $customer_phone\n"
        . "Room Type: $room_type\n"
        . "Room Number: $room_number\n"
        . "Check-in: $check_in\n"
        . "Check-out: $check_out\n"
        . "Guests: $guests\n"
        . "Total Price: $total_price\n"
        . "Special Requests: $special_requests\n";

    $subject_customer = 'Your Booking Confirmation - ' . $hotel_name;
    $message_customer = "Dear $customer_name,\n\nThank you for your booking at $hotel_name! Here are your booking details:\n\n"
        . "Room Type: $room_type\n"
        . "Room Number: $room_number\n"
        . "Check-in: $check_in\n"
        . "Check-out: $check_out\n"
        . "Guests: $guests\n"
        . "Total Price: $total_price\n"
        . "Special Requests: $special_requests\n\n"
        . "We look forward to welcoming you!\n\n$hotel_name";

    $from_email = get_option('sb_smtp_username', '<EMAIL>');
    $from_name = 'Hotel Southern Blue';
    $headers_admin = array();
    $headers_admin[] = 'Content-Type: text/plain; charset=UTF-8';
    $headers_admin[] = 'From: ' . $from_name . ' <' . $from_email . '>';
    if (!empty($customer_email)) {
        $headers_admin[] = 'Reply-To: ' . $customer_name . ' <' . $customer_email . '>';
    }
    $headers_customer = array();
    $headers_customer[] = 'Content-Type: text/plain; charset=UTF-8';
    $headers_customer[] = 'From: ' . $from_name . ' <' . $from_email . '>';

    $customer_sent = false;
    $admin_sent = false;
    // Send to admin
    if (!empty($admin_email)) {
        $admin_sent = wp_mail($admin_email, $subject_admin, $message_admin, $headers_admin);
    }
    // Send to customer
    if (!empty($customer_email)) {
        $customer_sent = wp_mail($customer_email, $subject_customer, $message_customer, $headers_customer);
    }

    // Only update DB if booking_id is real
    if (!empty($booking_id)) {
        $wpdb->update(
            $wpdb->prefix . 'sb_bookings',
            array(
                'customer_notified' => $customer_sent ? 1 : 0,
                'admin_notified' => $admin_sent ? 1 : 0
            ),
            array('id' => $booking_id)
        );
    }
    return $customer_sent && $admin_sent;
}

// Add columns to bookings table for email notification status
function sb_add_email_notification_columns() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sb_bookings';
    
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'customer_notified'");
    if (empty($column_exists)) {
        $wpdb->query("ALTER TABLE {$table_name} ADD customer_notified TINYINT(1) DEFAULT 0");
    }
    
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'admin_notified'");
    if (empty($column_exists)) {
        $wpdb->query("ALTER TABLE {$table_name} ADD admin_notified TINYINT(1) DEFAULT 0");
    }
}
add_action('after_switch_theme', 'sb_add_email_notification_columns');

// Modify create_booking function to send emails
function sb_create_booking() {
    if (!isset($_POST['booking_data'])) {
        wp_send_json_error('Missing booking data');
        return;
    }

    global $wpdb;
    $data = $_POST['booking_data'];

    // Sanitize and validate data
    $booking_data = [
        'room_id' => isset($data['room_id']) ? intval($data['room_id']) : 0,
        'room_type' => isset($data['room_type']) ? sanitize_text_field($data['room_type']) : '',
        'customer_name' => isset($data['name']) ? sanitize_text_field($data['name']) : '',
        'customer_email' => isset($data['email']) ? sanitize_email($data['email']) : '',
        'customer_phone' => isset($data['phone']) && !empty($data['phone']) ? sanitize_text_field($data['phone']) : 'N/A',
        'check_in' => isset($data['check_in']) ? sanitize_text_field($data['check_in']) : '',
        'check_out' => isset($data['check_out']) ? sanitize_text_field($data['check_out']) : '',
        'guests' => isset($data['guests']) ? intval($data['guests']) : 1,
        'total_price' => isset($data['total_price']) ? floatval($data['total_price']) : 0.0,
        'booking_status' => 'pending',
        'payment_status' => 'unpaid',
        'special_requests' => isset($data['special_requests']) ? sanitize_textarea_field($data['special_requests']) : ''
    ];

    // If room_id is not set, try to look up an available room by type and date
    if (empty($booking_data['room_id'])) {
        $room_type = '';
        if (isset($data['room_type'])) {
            $room_type = sanitize_text_field($data['room_type']);
        } elseif (isset($booking_data['room_type'])) {
            $room_type = sanitize_text_field($booking_data['room_type']);
        }
        if (!empty($room_type) && !empty($booking_data['check_in']) && !empty($booking_data['check_out'])) {
            $room = $wpdb->get_row($wpdb->prepare(
                "SELECT id FROM {$wpdb->prefix}sb_rooms WHERE room_type = %s AND status = 'available' LIMIT 1",
                $room_type
            ));
            if ($room) {
                $booking_data['room_id'] = $room->id;
            }
        }
    }

    // Calculate total price: price per night * number of nights
    if ((empty($booking_data['total_price']) || $booking_data['total_price'] <= 0) && !empty($booking_data['check_in']) && !empty($booking_data['check_out'])) {
        $check_in = new DateTime($booking_data['check_in']);
        $check_out = new DateTime($booking_data['check_out']);
        $interval = $check_in->diff($check_out);
        $nights = $interval->days;
        $room_price = 0;
        // Try to get price from DB if room_id is set
        if (!empty($booking_data['room_id'])) {
            $room = $wpdb->get_row($wpdb->prepare("SELECT price FROM {$wpdb->prefix}sb_rooms WHERE id = %d", $booking_data['room_id']));
            if ($room) {
                $room_price = floatval($room->price);
            }
        }
        // If still zero, try to get price by room_type (fallback)
        if (empty($room_price) && !empty($booking_data['room_type'])) {
            $room_type = strtolower(trim($booking_data['room_type']));
            $room_type_map = [
                'deluxe rooms' => 2500,
                'twin beds' => 2200,
                'standard rooms' => 2000,
                'single rooms' => 1500,
                'bnb rooms' => 1800
            ];
            if (isset($room_type_map[$room_type])) {
                $room_price = $room_type_map[$room_type];
            }
        }
        if ($room_price > 0 && $nights > 0) {
            $booking_data['total_price'] = $room_price * $nights;
        } else {
            $booking_data['total_price'] = 0.0;
        }
    }

    // In test mode, if room_id is missing or 0, set a dummy value
    if (empty($booking_data['room_id'])) {
        $booking_data['room_id'] = 1; // Dummy room ID for test/demo
    }

    // Basic validation: all required fields must be present and valid
    $required_fields = ['room_id', 'customer_name', 'customer_email', 'customer_phone', 'check_in', 'check_out', 'guests', 'total_price'];
    foreach ($required_fields as $field) {
        if (!isset($booking_data[$field]) || $booking_data[$field] === '' || $booking_data[$field] === 0) {
            error_log('Booking error: missing or invalid field ' . $field . ' | Data: ' . print_r($booking_data, true));
            wp_send_json_error('Missing or invalid booking field: ' . $field);
            return;
        }
    }

    // --- Skip saving to database ---
    $booking_id = rand(10000, 99999); // Simulated booking ID
    // Send notifications using booking data directly
    $emails_sent = sb_send_booking_emails($booking_data);
    wp_send_json_success([
        'booking_id' => $booking_id,
        'emails_sent' => $emails_sent,
        'note' => 'Booking not saved to database (test mode)'
    ]);
}
add_action('wp_ajax_create_booking', 'sb_create_booking');
add_action('wp_ajax_nopriv_create_booking', 'sb_create_booking');

// Get booking status
function sb_get_booking_status() {
    if (!isset($_POST['booking_id'])) {
        wp_send_json_error('Missing booking ID');
        return;
    }

    global $wpdb;
    $booking_id = intval($_POST['booking_id']);
    
    $booking = $wpdb->get_row($wpdb->prepare(
        "SELECT b.*, p.status as payment_status, p.amount as paid_amount 
        FROM {$wpdb->prefix}sb_bookings b 
        LEFT JOIN {$wpdb->prefix}sb_payments p ON b.id = p.booking_id 
        WHERE b.id = %d",
        $booking_id
    ));

    if ($booking) {
        wp_send_json_success(['booking' => $booking]);
    } else {
        wp_send_json_error('Booking not found');
    }
}
add_action('wp_ajax_get_booking_status', 'sb_get_booking_status');
add_action('wp_ajax_nopriv_get_booking_status', 'sb_get_booking_status');

// Enqueue scripts and styles
function sb_enqueue_booking_scripts() {
    // Only enqueue our booking script, Bootstrap is already enqueued in southernblue_enqueue_scripts
    wp_enqueue_script('sb-booking', get_template_directory_uri() . '/js/booking.js', array('jquery', 'bootstrap'), '1.0', true);
    wp_localize_script('sb-booking', 'sbBooking', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('sb-booking-nonce')
    ));
    // Enqueue gallery script
    wp_enqueue_script('sb-gallery', get_template_directory_uri() . '/js/gallery.js', array('jquery', 'bootstrap'), '1.0', true);
}
add_action('wp_enqueue_scripts', 'sb_enqueue_booking_scripts');

// Enqueue coffee shop styles and scripts
function sb_enqueue_coffee_shop_scripts() {
    // Only load on front page and coffee shop page
    if (is_front_page() || is_page_template('coffee-shop.php')) {
        // Owl Carousel CSS
        wp_enqueue_style('owl-carousel', 'https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css', array(), '2.3.4');
        wp_enqueue_style('owl-carousel-theme', 'https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css', array(), '2.3.4');
        // Owl Carousel JS (defer)
        wp_enqueue_script('owl-carousel', 'https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js', array('jquery'), '2.3.4', true);
        // Custom coffee shop JS (defer)
        wp_enqueue_script('sb-coffee-shop', get_template_directory_uri() . '/js/coffee-shop.js', array('jquery', 'owl-carousel'), '1.0.0', true);
// Remove jQuery Migrate if not needed
add_filter('wp_default_scripts', function($scripts){
    if (!is_admin() && isset($scripts->registered['jquery']) && isset($scripts->registered['jquery-migrate'])) {
        $scripts->registered['jquery']->deps = array_diff($scripts->registered['jquery']->deps, ['jquery-migrate']);
    }
});

// Add defer/async to enqueued scripts for performance
add_filter('script_loader_tag', function($tag, $handle) {
    $defer = [
        'bootstrap', 'popper', 'sb-booking', 'sb-gallery', 'owl-carousel', 'sb-coffee-shop', 'southernblue-gallery'
    ];
    foreach ($defer as $d) {
        if ($handle === $d) {
            return str_replace(' src', ' defer src', $tag);
        }
    }
    return $tag;
}, 10, 2);
    }
}
add_action('wp_enqueue_scripts', 'sb_enqueue_coffee_shop_scripts');

// Create coffee shop page on theme activation
function sb_create_coffee_shop_page() {
    $coffee_shop_page = array(
        'post_title'    => 'Coffee Shop',
        'post_content'  => '',
        'post_status'   => 'publish',
        'post_type'     => 'page',
        'post_name'     => 'coffee-shop'
    );

    $page_exists = get_page_by_path('coffee-shop');
    
    if (!$page_exists) {
        $page_id = wp_insert_post($coffee_shop_page);
        
        if ($page_id) {
            update_post_meta($page_id, '_wp_page_template', 'coffee-shop.php');
        }
    }
}
add_action('after_switch_theme', 'sb_create_coffee_shop_page');

// Utility: where to store the generated PDF menu (within wp-uploads so it is always writable)
if ( ! function_exists( 'sb_get_pdf_menu_path' ) ) {
    function sb_get_pdf_menu_path() {
        $upload_dir = wp_upload_dir();
        $dir        = trailingslashit( $upload_dir['basedir'] ) . 'menu';

        // Create menu sub-folder if it doesn't exist.
        if ( ! is_dir( $dir ) ) {
            wp_mkdir_p( $dir );
        }

        return $dir . '/coffee-menu.pdf';
    }
}

if ( ! function_exists( 'sb_get_pdf_menu_url' ) ) {
    function sb_get_pdf_menu_url() {
        $upload_dir = wp_upload_dir();
        return trailingslashit( $upload_dir['baseurl'] ) . 'menu/coffee-menu.pdf';
    }
}

// Generate PDF menu
function sb_generate_pdf_menu() {
    if (!class_exists('TCPDF')) {
        require_once get_template_directory() . '/lib/tcpdf/tcpdf.php';
    }
    
    $html = file_get_contents(get_template_directory() . '/menu/coffee-menu.html');
    
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    
    $pdf->SetCreator(PDF_CREATOR);
    $pdf->SetAuthor('Southern Blue Hotel');
    $pdf->SetTitle('Coffee Shop Menu');
    
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    
    $pdf->AddPage();
    
    $pdf->writeHTML($html, true, false, true, false, '');
    
    $pdf->Output( sb_get_pdf_menu_path(), 'F' );
}
add_action('after_switch_theme', 'sb_generate_pdf_menu');

// Register Coffee Blog Custom Post Type
function sb_register_coffee_blog_post_type() {
    $labels = array(
        'name'               => 'Coffee Stories',
        'singular_name'      => 'Coffee Story',
        'menu_name'          => 'Coffee Blog',
        'add_new'           => 'Add New Story',
        'add_new_item'      => 'Add New Coffee Story',
        'edit_item'         => 'Edit Coffee Story',
        'new_item'          => 'New Coffee Story',
        'view_item'         => 'View Coffee Story',
        'search_items'      => 'Search Coffee Stories',
        'not_found'         => 'No coffee stories found',
        'not_found_in_trash'=> 'No coffee stories found in trash',
    );

    $args = array(
        'labels'              => $labels,
        'public'              => true,
        'has_archive'         => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'show_in_rest'        => true, // Enable Gutenberg editor
        'menu_icon'           => 'dashicons-coffee',
        'menu_position'       => 5,
        'supports'            => array('title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments'),
        'rewrite'            => array('slug' => 'coffee-stories'),
        'capability_type'    => 'post',
    );

    register_post_type('coffee_story', $args);

    // Register categories for coffee stories
    register_taxonomy('coffee_category', 'coffee_story', array(
        'label'              => 'Coffee Categories',
        'hierarchical'       => true,
        'show_in_rest'       => true,
        'rewrite'            => array('slug' => 'coffee-category'),
    ));

    // Register tags for coffee stories
    register_taxonomy('coffee_tag', 'coffee_story', array(
        'label'              => 'Coffee Tags',
        'hierarchical'       => false,
        'show_in_rest'       => true,
        'rewrite'            => array('slug' => 'coffee-tag'),
    ));
}
add_action('init', 'sb_register_coffee_blog_post_type');

// Add custom roles and capabilities for coffee bloggers
function sb_add_coffee_blogger_role() {
    add_role('coffee_blogger', 'Coffee Blogger', array(
        'read'                   => true,
        'edit_posts'             => true,
        'delete_posts'           => true,
        'publish_posts'          => true,
        'upload_files'           => true,
        'edit_published_posts'   => true,
        'delete_published_posts' => true,
    ));
}
add_action('init', 'sb_add_coffee_blogger_role');

// Add meta box for coffee story details
function sb_add_coffee_story_meta_box() {
    add_meta_box(
        'coffee_story_details',
        'Coffee Story Details',
        'sb_coffee_story_meta_box_callback',
        'coffee_story',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'sb_add_coffee_story_meta_box');

// Meta box callback function
function sb_coffee_story_meta_box_callback($post) {
    wp_nonce_field('sb_coffee_story_nonce', 'coffee_story_nonce');
    
    $coffee_type = get_post_meta($post->ID, '_coffee_type', true);
    $rating = get_post_meta($post->ID, '_rating', true);
    $location = get_post_meta($post->ID, '_location', true);
    
    ?>
    <div class="coffee-story-meta">
        <p>
            <label for="coffee_type">Coffee Type:</label>
            <input type="text" id="coffee_type" name="coffee_type" value="<?php echo esc_attr($coffee_type); ?>" class="widefat">
        </p>
        <p>
            <label for="rating">Rating (1-5):</label>
            <select id="rating" name="rating" class="widefat">
                <?php for($i = 1; $i <= 5; $i++) : ?>
                    <option value="<?php echo $i; ?>" <?php selected($rating, $i); ?>><?php echo str_repeat('★', $i) . str_repeat('☆', 5-$i); ?></option>
                <?php endfor; ?>
            </select>
        </p>
        <p>
            <label for="location">Location:</label>
            <input type="text" id="location" name="location" value="<?php echo esc_attr($location); ?>" class="widefat">
        </p>
    </div>
    <?php
}

// Save meta box data
function sb_save_coffee_story_meta($post_id) {
    if (!isset($_POST['coffee_story_nonce']) || !wp_verify_nonce($_POST['coffee_story_nonce'], 'sb_coffee_story_nonce')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    $fields = array('coffee_type', 'rating', 'location');
    
    foreach($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post_coffee_story', 'sb_save_coffee_story_meta');

// Ensure the PDF menu is present (generate it on-the-fly if it is missing)
function sb_ensure_pdf_menu_exists() {
    $pdf_path = sb_get_pdf_menu_path();
    if ( ! file_exists( $pdf_path ) ) {
        sb_generate_pdf_menu();
    }
}
add_action('init', 'sb_ensure_pdf_menu_exists');

// Configure WP Mail SMTP plugin if it's active
function configure_wp_mail_smtp_plugin() {
    // Check if WP Mail SMTP is active
    if (function_exists('wp_mail_smtp')) {
        // Set default SMTP settings for Gmail
        $options = array(
            'mail' => array(
                'from_email' => '<EMAIL>',
                'from_name' => 'Hotel Southern Blue',
                'mailer' => 'smtp',
                'return_path' => false,
            ),
            'smtp' => array(
                'host' => 'smtp.gmail.com',
                'port' => 587,
                'encryption' => 'tls',
                'auth' => true,
                'user' => '<EMAIL>',
                'pass' => '', // This needs to be set manually with Gmail App Password
            ),
        );

        // Update with the provided credentials
        $options['smtp']['pass'] = '@password..';
        update_option('wp_mail_smtp', $options);
    }
}
add_action('plugins_loaded', 'configure_wp_mail_smtp_plugin');

// Function to update email config file
function update_email_config_file($post_data) {
    $config_file = get_template_directory() . '/email-config.php';

    $use_smtp = isset($post_data['use_smtp']) ? 'true' : 'false';
    $smtp_host = sanitize_text_field($post_data['smtp_host']);
    $smtp_port = intval($post_data['smtp_port']);
    $smtp_secure = sanitize_text_field($post_data['smtp_secure']);
    $smtp_username = sanitize_text_field($post_data['smtp_username']);
    $smtp_password = sanitize_text_field($post_data['smtp_password']);

    $config_content = '<?php
/**
 * Email Configuration for Hotel Southern Blue
 * Auto-generated from WordPress admin settings
 */

// Email Configuration
define(\'HOTEL_SMTP_HOST\', \'' . $smtp_host . '\');
define(\'HOTEL_SMTP_PORT\', ' . $smtp_port . ');
define(\'HOTEL_SMTP_SECURE\', \'' . $smtp_secure . '\');
define(\'HOTEL_SMTP_USERNAME\', \'' . $smtp_username . '\');
define(\'HOTEL_SMTP_PASSWORD\', \'' . $smtp_password . '\');

// Email Settings
define(\'HOTEL_FROM_EMAIL\', \'' . $smtp_username . '\');
define(\'HOTEL_FROM_NAME\', \'Hotel Southern Blue\');
define(\'HOTEL_ADMIN_EMAIL\', \'<EMAIL>\');

// Enable/Disable SMTP
define(\'HOTEL_USE_SMTP\', ' . $use_smtp . ');
';

    file_put_contents($config_file, $config_content);
}