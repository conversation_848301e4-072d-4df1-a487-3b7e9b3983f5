<?php
// test-db-connection.php
// Place this file in your theme directory and access it via browser to test DB connection

define('WP_USE_THEMES', false);
require_once('../../../wp-load.php'); // Adjust path if needed

global $wpdb;

header('Content-Type: text/plain');

echo "Testing WordPress Database Connection...\n";

// Try a simple query
$result = $wpdb->get_var("SELECT NOW()");

if ($result) {
    echo "✅ Database connection successful!\n";
    echo "Current DB Time: $result\n";
} else {
    echo "❌ Database connection failed.\n";
    echo "Error: ".$wpdb->last_error."\n";
}

// Optionally, show DB host and user (for debugging only)
// echo "DB Host: ".DB_HOST."\n";
// echo "DB User: ".DB_USER."\n";
