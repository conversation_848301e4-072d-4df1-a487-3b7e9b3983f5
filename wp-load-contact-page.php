<?php
// Programmatically create a WordPress page with the [sb_contact_form] shortcode
// Place this script in the WordPress root directory (where wp-load.php is)
require_once(__DIR__ . '/wp-load.php');
$page_title = 'Contact Us';
$page_content = '[sb_contact_form]';
$page_slug = 'contact-us';

// Check if the page already exists
$page = get_page_by_path($page_slug);
if (!$page) {
    $page_id = wp_insert_post(array(
        'post_title'   => $page_title,
        'post_name'    => $page_slug,
        'post_content' => $page_content,
        'post_status'  => 'publish',
        'post_type'    => 'page',
    ));
    if ($page_id && !is_wp_error($page_id)) {
        echo "Contact page created successfully!\n";
    } else {
        echo "Failed to create contact page.\n";
    }
} else {
    echo "Contact page already exists.\n";
}
